import React, { useState } from "react";
import axios from "axios";
import "./SimpleQuestionGenerator.css";

const SimpleQuestionGenerator = () => {
  const [content, setContent] = useState("");
  const [questionType, setQuestionType] = useState("MCQ");
  const [numQuestions, setNumQuestions] = useState(5);
  const [isLoading, setIsLoading] = useState(false);
  const [generatedQuestions, setGeneratedQuestions] = useState([]);
  const [error, setError] = useState("");

  const questionTypes = [
    { value: "MCQ", label: "Multiple Choice Questions" },
    { value: "Short Answer Type I", label: "Short Answer Type I" },
    { value: "Short Answer Type II", label: "Short Answer Type II" },
    { value: "Long Answer Type I", label: "Long Answer Type I" },
    { value: "Long Answer Type II", label: "Long Answer Type II" }
  ];

  const handleGenerateQuestions = async () => {
    if (!content.trim()) {
      setError("Please enter some content to generate questions from.");
      return;
    }

    setIsLoading(true);
    setError("");
    setGeneratedQuestions([]);

    try {
      console.log("Sending request with params:", {
        questionType: questionType,
        numQuestionsValue: numQuestions,
        enterTheText: content,
        showContent: "true"
      });

      const response = await axios.get("http://127.0.0.1:5000/api/test-generate-question/", {
        params: {
          questionType: questionType,
          numQuestionsValue: numQuestions,
          enterTheText: content,
          showContent: "true"
        }
      });

      console.log("Response received:", response.data);

      if (response.data && response.data.question) {
        // Split the question response into individual questions
        const questions = response.data.question.split('\n').filter(q => q.trim().length > 0);
        console.log("Parsed questions:", questions);
        setGeneratedQuestions(questions);
      } else {
        console.log("No question data in response:", response.data);
        setError("No questions were generated. Please try again.");
      }
    } catch (err) {
      console.error("Error generating questions:", err);
      console.error("Error response:", err.response?.data);
      setError(`Failed to generate questions: ${err.response?.data?.error || err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setContent("");
    setQuestionType("MCQ");
    setNumQuestions(5);
    setGeneratedQuestions([]);
    setError("");
  };

  return (
    <div className="simpleQuestionGenerator">
      <div className="generatorHeader">
        <h2>Simple Question Generator</h2>
        <p>Enter your content and get questions generated instantly!</p>
      </div>

      <div className="generatorForm">
        {error && (
          <div className="errorMessage">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM11 15H9V13H11V15ZM11 11H9V5H11V11Z" fill="currentColor"/>
            </svg>
            {error}
          </div>
        )}

        <div className="formSection">
          <label htmlFor="content">Enter your content:</label>
          <textarea
            id="content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Paste your text, passage, or content here..."
            rows={8}
            className="contentInput"
          />
        </div>

        <div className="formSection">
          <div className="controlsRow">
            <div className="controlGroup">
              <label htmlFor="questionType">Question Type:</label>
              <select
                id="questionType"
                value={questionType}
                onChange={(e) => setQuestionType(e.target.value)}
                className="selectInput"
              >
                {questionTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="controlGroup">
              <label htmlFor="numQuestions">Number of Questions:</label>
              <input
                id="numQuestions"
                type="number"
                value={numQuestions}
                onChange={(e) => setNumQuestions(parseInt(e.target.value) || 1)}
                min="1"
                max="20"
                className="numberInput"
              />
            </div>
          </div>
        </div>

        <div className="formActions">
          <button
            type="button"
            onClick={handleGenerateQuestions}
            disabled={isLoading || !content.trim()}
            className="generateButton"
          >
            {isLoading ? "Generating..." : "Generate Questions"}
          </button>
          <button
            type="button"
            onClick={handleReset}
            className="resetButton"
          >
            Reset
          </button>
        </div>
      </div>

      {generatedQuestions.length > 0 && (
        <div className="resultsSection">
          <h3>Generated Questions</h3>
          <div className="questionsList">
            {generatedQuestions.map((question, index) => (
              <div key={index} className="questionItem">
                <div className="questionNumber">Question {index + 1}</div>
                <div className="questionText">{question}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleQuestionGenerator; 