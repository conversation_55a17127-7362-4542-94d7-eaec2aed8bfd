@echo off
echo Starting Frontend and Backend servers...

REM Start Frontend
start "Frontend Server" cmd /k "cd grade_fe && npm run dev"

REM Wait a moment
timeout /t 3 /nobreak >nul

REM Start Backend
start "Backend Server" cmd /k "cd grade_be && myvenv\Scripts\python.exe manage.py runserver"

echo Both servers are starting in separate windows...
echo Frontend: http://localhost:5173/
echo Backend: http://localhost:8000/
pause
