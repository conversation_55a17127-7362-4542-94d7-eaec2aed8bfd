import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './GenerateQuestions.css';
import { authService } from '../Authentication/authService';

const TOPICS = [
  "Arrays",
  "Backtracking",
  "Bit Manipulation",
  "Divide and Conquer",
  "Dynamic Programming",
  "Graphs",
  "Greedy Algorithms",
  "Hash Tables",
  "Heaps / Priority Queues",
  "Linked Lists",
  "Math",
  "Object-Oriented Programming",
  "Queues",
  "Recursion",
  "Searching",
  "Sliding Window",
  "Sorting",
  "Stacks",
  "Strings",
  "Trees (Binary/BST)",
  "Two Pointers",
  "Union Find / Disjoint Set",
  "Others"
];

const GenerateQuestions = () => {
  const [topic, setTopic] = useState(TOPICS[0]);
  const [difficulty, setDifficulty] = useState('Easy');
  const [questions, setQuestions] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchAllQuestions = async () => {
      try {
        const res = await axios.get('http://localhost:8000/api/questions/');
        setQuestions(res.data || []);
      } catch (err) {
        console.error('Failed to load questions:', err);
        setQuestions([]);
      }
    };

    fetchAllQuestions();

    const cleanup = () => localStorage.removeItem('generatedQuestions');
    window.addEventListener('beforeunload', cleanup);

    return () => {
      window.removeEventListener('beforeunload', cleanup);
    };
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const res = await axios.post('http://localhost:8000/api/generate_questions/', {
        topic,
        difficulty,
      });
      const generated = res.data.generated_questions || [];
      console.log('Generated questions response:', generated);

      localStorage.setItem('generatedQuestions', JSON.stringify(generated));

      // Redirect to question list page
      if (!authService.isTestMode()) {
        navigate('/programming');
      }

    } catch (err) {
      console.error('Failed to generate questions:', err);
      setQuestions([]);
    }
  };

  const handleSelectQuestion = (question) => {
    navigate(`/programming/code-editor/${question.id}`, { state: { question } });
  };

  return (
    <>
      <div className="generate-header">
        <h2>Generate Coding Questions</h2>
        <form className="generate-form" onSubmit={handleSubmit}>
          <select
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            required
          >
            {TOPICS.map((t) => (
              <option key={t} value={t}>
                {t}
              </option>
            ))}
          </select>

          <select value={difficulty} onChange={(e) => setDifficulty(e.target.value)}>
            <option value="Easy">Easy</option>
            <option value="Medium">Medium</option>
            <option value="Hard">Hard</option>
          </select>

          <button type="submit">Generate</button>
        </form>
      </div>

      <div className="generate-container">
        <div className="question-list">
          {Array.isArray(questions) && questions.length > 0 ? (
            questions.map((q) => (
              <div
                key={q.id}
                className="question-card"
                onClick={() => handleSelectQuestion(q)}
              >
                <h4>{q.title}</h4>
                <p>{q.description}</p>
              </div>
            ))
          ) : (
            <p>No questions available.</p>
          )}
        </div>
      </div>
    </>
  );
};

export default GenerateQuestions;
