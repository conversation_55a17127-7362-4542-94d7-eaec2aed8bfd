.simpleQuestionGenerator {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.generatorHeader {
  text-align: center;
  margin-bottom: 30px;
}

.generatorHeader h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2rem;
}

.generatorHeader p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.generatorForm {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.errorMessage {
  background: #fee;
  color: #c53030;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  border: 1px solid #fed7d7;
}

.formSection {
  margin-bottom: 24px;
}

.formSection label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2d3748;
}

.contentInput {
  width: 100%;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.5;
  resize: vertical;
  min-height: 120px;
  transition: border-color 0.2s;
}

.contentInput:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.controlsRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.controlGroup {
  display: flex;
  flex-direction: column;
}

.selectInput,
.numberInput {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.selectInput:focus,
.numberInput:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.formActions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 30px;
}

.generateButton,
.resetButton {
  padding: 14px 28px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.generateButton {
  background: #4299e1;
  color: white;
}

.generateButton:hover:not(:disabled) {
  background: #3182ce;
  transform: translateY(-1px);
}

.generateButton:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
}

.resetButton {
  background: #e2e8f0;
  color: #4a5568;
}

.resetButton:hover {
  background: #cbd5e0;
  transform: translateY(-1px);
}

.resultsSection {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.resultsSection h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.questionsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.questionItem {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s;
}

.questionItem:hover {
  border-color: #4299e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.questionNumber {
  font-weight: 600;
  color: #4299e1;
  margin-bottom: 8px;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.questionText {
  color: #2d3748;
  line-height: 1.6;
  font-size: 16px;
}

@media (max-width: 768px) {
  .simpleQuestionGenerator {
    padding: 15px;
  }
  
  .generatorForm {
    padding: 20px;
  }
  
  .controlsRow {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .formActions {
    flex-direction: column;
  }
  
  .generateButton,
  .resetButton {
    width: 100%;
  }
} 